import sys

# NOTE: ADDDED BELOW CODE SO THAT THE OUTPUT IS REDIRECTED TO A FILE IN WINDOWED MODE EXE AND FOR CONSOLE MODE IT WORKS AS IT IS, THIS CODE IS ADDED CAUSE SPEEDTEST MODULE WAS RAISING ERROR RELATED TO THE ATTRIBUTE WHICH ONLY COMES WHEN WE RUN EXE IN CONSOLE MODE.
# Redirect stdout and stderr before any imports
try:
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Logs")
    os.makedirs(log_dir, exist_ok=True)
    log_file_path = os.path.join(log_dir, "console_output.log")
    
    log_file = open(log_file_path, "w", encoding="utf-8")
    if not sys.stdout:
        sys.stdout = log_file
    if not sys.stderr:
        sys.stderr = log_file
except Exception as e:
    pass  # Silently continue if redirection fails

import argparse
from datetime import datetime
import random
import string
from PyQt5.QtWidgets import QApplication
from FileProcessor import CFileProcessor
from CustomLogger import C<PERSON>ogger
from CustomHelper import CLicenseHelper, CGeneralInfoHelper, CGeneralHelper, CTallyExportHelper, CArgumentParser, CResoucedataHelper, CRunDiagnostics, process_xml_files, CBackupFileManager
from myFileHelper import CFileHelper
import os
from PyQt5.QtGui import QIcon
import traceback
from ReportShowDialogUI import ExcelReportDialog
from PopupWindow import CPopupWindow
from ActivityLogHelper import CActivityReportMerger
from DiagnosticsUI import CDiagnosticsWindow
from TallyHistoryUI import ShowActionableActivityUI

def GetArguments():
    parser = argparse.ArgumentParser(description="Accuvelocity File Processing CLI Application")

    parser.add_argument(
        'files',
        nargs='?',  # '?' makes this argument optional
        default="",
        type=str,
        help="String containing file paths separated by semicolons (;)"
    )
    parser.add_argument(
        "--xml_files",
        nargs="?",  # '?' makes it optional
        default="",
        type=str,
        help="String containing XML file path or directory path"
    )
    parser.add_argument(
        '--log-dir',
        type=str,
        default=None,
        help="Directory path where logs will be stored (default: 'Logs')"
    )

    parser.add_argument(
        '--enable-export-stockitem',
        action='store_true',  # This will store True if the flag is passed, otherwise False
        help="Enable Export Customer StockItem Inventory mode (default: False)"
    )

    parser.add_argument(
        '--version',
        action='store_true',
        help="Display the application version and exit."
    )
    parser.add_argument(
        '--run-diagnostics',
        action='store_true',
        help="Run system diagnostics and display relevant information."
    )
    parser.add_argument(
        '--purchase-with-inventory',
        action='store_true',
        help="Enable this option to process invoices using the 'Purchase With Inventory' voucher entry in Tally."
    )
    parser.add_argument(
        '--purchase-without-inventory',
        action='store_true',
        help="Enable this option to process invoices using the 'Purchase Without Inventory' voucher entry in Tally."
    )
    parser.add_argument(
        '--bank-statement',
        action='store_true',
        help="Enable this option to process Bank Statement using Bank voucher types in Tally."
    )
    parser.add_argument(
        '--delivery-note',
        action='store_true',
        help="Enable this option to process quotations using the 'Delivery Note' voucher entry in Tally."
    )
    parser.add_argument(
        '--receipt-note',
        action='store_true',
        help="Enable this option to process document using the 'Receipt Note' voucher entry in Tally."
    )
    parser.add_argument(
        '--purchase-order',
        action='store_true',
        help="Enable this option to process document using the 'Purchase Order' voucher entry in Tally."
    )
    parser.add_argument(
        '--Journal',
        action='store_true',
        help="Enable this option to process Invoice using the 'Journal Template' voucher entry in Tally."
    )
    parser.add_argument(
        '--get-activity-report',
        action='store_true',
        help="Enable this option to V1: Create Activity Log Excel Report ; V2: Open Actionable Activity GUI."
    )
    parser.add_argument(
        '--multiple-vendor',
        action='store_true',
        help="Enable this option When Multiple Vendor Single PDF Document."
    )
    parser.add_argument(
        '--track-backup-record',
        action='store_true',
        help="Enable this option track number of backup taken."
    )
    parser.add_argument(
        '--generate-backup-report',
        action='store_true',
        help="Enable this option genrate backup report."
    )
    
    # Export options (mutually exclusive)
    export_group = parser.add_mutually_exclusive_group()
    export_group.add_argument(
        '--export-all',
        action='store_true',
        help="Export all data in both detailed and abstract formats."
    )
    export_group.add_argument(
        '--export-abstract',
        action='store_true',
        help="Export only abstract data."
    )
    export_group.add_argument(
        '--export-detailed',
        action='store_true',
        help="Export only detailed data."
    )
    export_group.add_argument(
        '--export-filtered-stock-item',
        action='store_true',
        help="Export only detailed data."
    )
    export_group.add_argument(
        '--export-daybook-data',
        action='store_true',
        help="Export only detailed data."
    )
    return parser.parse_args()

def main():
    try:
        # ------------------- Initialize Application and Resources -------------------
        app = QApplication([])

        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        exe_dir = CFileHelper._get_executable_directory()
        strIconPath = os.path.join(strResourceDirpath, "AvLogo.ico")

        app.setWindowIcon(QIcon(strIconPath))

        args = GetArguments()

        strVoucherType = None
        command_string = args.files
        log_dir = args.log_dir
        bEnableStockItemExport = args.enable_export_stockitem # client  stock item inventory export
        bIsMultipleVendorSplitAlgo = True if args.multiple_vendor else False

        if not log_dir:
            log_dir = os.path.join(exe_dir, "Logs")

        # ------------------- Initialize Logging -------------------
        CLogger.MCSetupLogging(strLogsDirPath=log_dir)

        # ------------------- Backup & Diagnostics -------------------
        if args.track_backup_record:
            CBackupFileManager.startBackupManager()
            return
        
        if args.generate_backup_report:
            CBackupFileManager.MSShowBackupReportGUI()
            return

        if args.run_diagnostics:
            window = CDiagnosticsWindow()
            window.show()
            sys.exit(app.exec_())

        # ------------------- Set Tally Voucher Type -------------------
        if args.purchase_with_inventory:
            strVoucherType = "PV_WITH_INVENTORY"
        elif args.purchase_without_inventory:
            strVoucherType = "PV_WITHOUT_INVENTORY"
        elif args.delivery_note:
            strVoucherType = "DELIVERY_NOTE"
        elif args.Journal:
            strVoucherType = "JOURNAL_VOUCHER"
        elif args.bank_statement:
            strVoucherType = "BANK_STATEMENT"
        elif args.receipt_note:
            strVoucherType = "RECEIPT_NOTE"
        elif args.purchase_order:
            strVoucherType = "PURCHASE_ORDER"
        else:
            strVoucherType = "PV_WITH_INVENTORY"  # Default

        # ------------------- Log Initial Details -------------------
        CLogger.MCWriteLog("info", f"Command string received: {command_string}")
        strVersionDetail = CGeneralInfoHelper.MSGetVersionDetails()
        CLogger.MCWriteLog("info", f"Current Version: {strVersionDetail}")
        dictLicenseData = CLicenseHelper.MSVerifyLicense()
        strUserToken = dictLicenseData["Token"]

        # ------------------- XML Export Flow -------------------
        if args.xml_files:
            dictUserConfig = CResoucedataHelper.MSGetUserConfig()
            
            # Extract configuration values
            strTallyServerUrl = dictUserConfig.get("ExportRequest", {}).get("ReqUrl")

            # Check if it's a valid path
            if not os.path.exists(args.xml_files):
                raise ValueError("Error: Please provide a valid XML file path or directory path.")

            # If it's a directory, pass it to process_xml_files
            if os.path.isdir(args.xml_files):
                process_xml_files(input_dir=args.xml_files, output_dir=r"Manual Import XML", url = strTallyServerUrl, strToken = strUserToken)

            # If it's an XML file, call MSExportTallyData directly
            elif os.path.isfile(args.xml_files) and args.xml_files.lower().endswith(".xml"):
                input_path = args.xml_files
                output_dir = os.path.join(exe_dir, "Manual Import XML")
                
                # Ensure the output directory exists
                os.makedirs(output_dir, exist_ok=True)
                
                output_path = os.path.join(output_dir, os.path.basename(input_path))
                
                # Create the file first
                with open(output_path, 'w') as f:
                    pass
                # Call the method
                strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
                    strRequestXMLPath=input_path,
                    strResponseXMLPath=output_path,
                    url=strTallyServerUrl
                )
                print(f"Processed: {input_path} -> {output_path}")

            else:
                raise ValueError("Error: Please provide a valid XML file path or directory path.")
        
        # ------------------- Activity Report Generation -------------------
        dictUserConfig = CResoucedataHelper.MSGetUserConfig()
        bIsAcitivityGUIV1 = dictUserConfig.get("ActivityUI", "V2") == "V1"
        bIsAcitivityGUIV2 = dictUserConfig.get("ActivityUI", "V2") == "V2"
        
        if args.get_activity_report:
            if bIsAcitivityGUIV1:
                
                strRequestDirPath = dictUserConfig.get("ExportRequest", {}).get("RequestDir", os.path.join(exe_dir, "Requests"))
                objActivityReportManager = CActivityReportMerger( main_directory=strRequestDirPath)
                lsExcelActivityLog = objActivityReportManager.merge_and_split_files()
                if lsExcelActivityLog:
                    strMessage = f"""Your Activity Log report has been created and saved here:\n"""
                    strMessage += """\nTo dive into your report, just Select a report and click on Show Report to view. If you're all set, hit the Close button to exit."""
            
                    dialog = ExcelReportDialog(lsExcelActivityLog, strMessage)
                    dialog.exec_()
                    return
                else:
                    strMessage = "AccuVelocity Document Report is not available in your system. Please begin processing documents to view your activity logs. \n\nIf you continue to face issues, feel free to reach out to our development team or contact <NAME_EMAIL>."
                    CPopupWindow(title="AccuVelocity Activity Log", message=strMessage, message_type="info")
                    return
            elif bIsAcitivityGUIV2:
                ShowActionableActivityUI()
                return
            else:
                raise ValueError("WARN: Please Provide Valid V1 or V2 for AccuVelocity Activity Feature.")
        
        # ------------------- Show Version Info and Exit -------------------
        if args.version:
            print(strVersionDetail)
            sys.exit(0)
            
        

        # ------------------- Export Related Operations -------------------
        if args.export_all or args.export_abstract or args.export_detailed or args.export_filtered_stock_item or args.export_daybook_data:
            strExportType = CArgumentParser.MSGetExportType(args)
            dictInstalledExeDetail=CResoucedataHelper.MSGetAccuvelocityConfig()
            lsalternative_bases=dictInstalledExeDetail.get("apiEndpoints",[])
            CTallyExportHelper.MSExportNSaveTallyData(strExportType, strUserToken,lsalternative_bases=lsalternative_bases)
            sys.exit(0)

        # ------------------- Prepare File Processing -------------------
        bDevMode = dictLicenseData['DEVELOPER_MODE']
        strCurrentTime = datetime.now().strftime("%H%M%S")
        formatted_customer_name = dictLicenseData["name"].replace(" ", "").upper() if not bDevMode else "AVDEVELOPER"
        strRandomAlpaNumeric = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        strClientREQID = f"REQ_CS{formatted_customer_name}_TS{strCurrentTime}_UID{strRandomAlpaNumeric}"
         
        

        # ------------------- File Processor Initialization -------------------
        objFileProcessor = CFileProcessor(
            strCommand=command_string,
            strUserToken=strUserToken,
            bTestMode=bDevMode,
            strVoucherType=strVoucherType,
            bEnableStockItemExport=bEnableStockItemExport,
            bIsMultipleVendorSplitAlgo=bIsMultipleVendorSplitAlgo,
            strClientREQID=strClientREQID,
            objClientReqGeneratedAt=CGeneralHelper.MSGetCurrentFormatTimeStamp()
        )
        objFileProcessor.process_documents()
        app.exec_()  # Ensure QApplication runs the event loop
        sys.exit(0)
    except Exception as e:
        CLogger.MCWriteLog("error", f"There was some error: {e}")
        CLogger.MCWriteLog("debug", f"{traceback.format_exc()}")
        sys.exit(1)
 

if __name__ == "__main__":
    main()
    # print(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
